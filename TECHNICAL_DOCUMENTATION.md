# VMwareCloak Enhanced - Technical Documentation

## Overview
This document provides detailed technical information about the VM detection evasion techniques implemented in VMwareCloak Enhanced v0.5.

## Detection Vectors & Evasion Techniques

### 1. Registry-Based Detection Evasion

#### SCSI Device Identifiers
**Detection Method**: Malware queries registry keys to identify virtual SCSI devices
**Registry Paths**:
- `HKLM:\HARDWARE\DEVICEMAP\Scsi\Scsi Port [0-3]\Scsi Bus [0-1]\Target Id 0\Logical Unit Id 0\Identifier`

**Evasion Technique**: Replace VMware-specific identifiers with random strings
```powershell
Set-ItemProperty -Path $registryPath -Name "Identifier" -Value $(Get-RandomString)
```

**Artifacts Targeted**:
- "VMware Virtual IDE Hard Drive"
- "VMware Virtual SCSI Hard Drive" 
- "VMWARE IDE CDR10"

#### BIOS Information Spoofing
**Detection Method**: WMI queries and registry inspection for BIOS vendor information
**Registry Paths**:
- `HKLM:\HARDWARE\DESCRIPTION\System\BIOS\BIOSVendor`
- `HKLM:\HARDWARE\DESCRIPTION\System\BIOS\BIOSVersion`
- `HKLM:\HARDWARE\DESCRIPTION\System\BIOS\SystemManufacturer`

**Evasion Technique**: Replace with legitimate hardware vendor information
```powershell
Set-ItemProperty -Path "HKLM:\HARDWARE\DESCRIPTION\System\BIOS" -Name "BIOSVendor" -Value "American Megatrends Inc."
```

#### System Information Modification
**Detection Method**: System manufacturer and product name queries
**Registry Paths**:
- `HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation\SystemManufacturer`
- `HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation\SystemProductName`

**Evasion Technique**: Replace with realistic system information from major OEMs

### 2. MAC Address Spoofing

#### Detection Method
Malware inspects network adapter MAC addresses for VM-specific patterns:
- VMware: `00:05:69`, `00:0C:29`, `00:1C:14`, `00:50:56`
- VirtualBox: `08:00:27`, `0A:00:27`
- Parallels: `00:1C:42`
- Xen: `00:16:3E`

#### Evasion Implementation
```powershell
function Get-RandomMacAddress {
    $vendorPrefixes = @(
        "00:1B:21",  # Intel
        "00:E0:4C",  # Realtek
        "00:26:B9"   # Intel
    )
    $prefix = $vendorPrefixes | Get-Random
    $suffix = "{0:X2}:{1:X2}:{2:X2}" -f (Get-Random -Maximum 256), (Get-Random -Maximum 256), (Get-Random -Maximum 256)
    return "$prefix:$suffix"
}
```

#### Registry Modification
MAC addresses are changed via network adapter registry entries:
- Path: `HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e972-e325-11ce-bfc1-08002be10318}\[AdapterKey]`
- Value: `NetworkAddress`

### 3. Process and Service Evasion

#### VMware Processes Terminated
- `vmtoolsd.exe` - VMware Tools service
- `vm3dservice.exe` - 3D graphics service
- `VGAuthService.exe` - Guest authentication service
- `VMwareService.exe` - Core VMware service
- `Vmwaretray.exe` - System tray application
- `Vmwareuser.exe` - User-mode service
- `TPAutoConnSvc.exe` - Auto-connect service

#### Multi-Hypervisor Process Termination
**VirtualBox**:
- `VBoxService.exe`
- `VBoxTray.exe`
- `VBoxControl.exe`

**QEMU/KVM**:
- `qemu-ga.exe` (QEMU Guest Agent)

**Hyper-V**:
- Integration services processes

### 4. File System Artifact Removal

#### VMware Driver Files
**Location**: `C:\Windows\System32\drivers\`
**Files Renamed**:
- `vmhgfs.sys` - Host-Guest File System
- `vmmemctl.sys` - Memory Control Driver
- `vmmouse.sys` - Mouse Driver
- `vmrawdsk.sys` - Raw Disk Driver
- `vmusbmouse.sys` - USB Mouse Driver
- `vm3*.sys` - 3D Graphics Drivers

#### System DLL Files
**Locations**: `C:\Windows\System32\` and `C:\Windows\SysWOW64\`
**Files Renamed**:
- `vmhgfs.dll`
- `VMWSU.DLL`
- `vm3*.dll`
- `vmGuestLib*.dll`

#### Directory Renaming
- `C:\Program Files\VMware\` → Random name
- `C:\Program Files\Common Files\VMware\` → Random name

### 5. WMI Data Source Manipulation

#### Target WMI Classes
- `Win32_Bios` - BIOS information
- `Win32_ComputerSystem` - System manufacturer/model
- `Win32_Processor` - CPU information
- `Win32_NetworkAdapterConfiguration` - Network settings

#### Implementation Strategy
Since WMI results cannot be directly modified, the script changes the underlying registry data that WMI queries read from.

### 6. Hardware Device Management

#### Virtual Device Hiding
**Method**: Disable virtual devices through Device Manager API
```powershell
Disable-PnpDevice -InstanceId $device.InstanceId -Confirm:$false
```

**Target Device Patterns**:
- `*VBox*`
- `*VMware*`
- `*Virtual*`
- `*VMMEMCTL*`
- `*vm3dmp*`

### 7. Network Adapter Modifications

#### Adapter Name Changes
Virtual network adapters are renamed to generic names:
- "VMware Network Adapter" → "Ethernet 1"
- "VirtualBox Host-Only Ethernet Adapter" → "Ethernet 2"

#### Description Modifications
Registry entries for adapter descriptions are updated:
- Path: Network adapter class registry keys
- Value: `DriverDesc`
- New Value: "Intel(R) Ethernet Connection"

## Multi-Hypervisor Support

### VirtualBox Evasion
**Registry Keys Removed**:
- `HKLM:\HARDWARE\ACPI\DSDT\VBOX__`
- `HKLM:\HARDWARE\ACPI\FADT\VBOX__`
- `HKLM:\SOFTWARE\Oracle\VirtualBox Guest Additions`
- `HKLM:\SYSTEM\ControlSet001\Services\VBoxGuest`

**Files Renamed**:
- VirtualBox driver files in System32\drivers
- VirtualBox executables and DLLs

### Hyper-V Evasion
**Registry Keys Targeted**:
- `HKLM:\SOFTWARE\Microsoft\Virtual Machine\Guest\Parameters`
- `HKLM:\SYSTEM\ControlSet001\Services\vmbus`
- `HKLM:\SYSTEM\ControlSet001\Services\VMBusHID`

### QEMU/KVM Evasion
**Process Termination**: `qemu-ga.exe`
**Registry Modifications**: QEMU-specific SCSI identifiers

## Validation Framework

### Detection Checks Performed
1. **Registry Artifact Scan**: Checks for remaining VM-specific registry keys
2. **Process Enumeration**: Verifies VM processes are terminated
3. **File System Scan**: Confirms VM files are renamed/removed
4. **MAC Address Validation**: Ensures non-VM MAC patterns
5. **Device Enumeration**: Checks for visible virtual devices

### Scoring System
- **Excellent (0 detections)**: Complete evasion achieved
- **Good (1-4 detections)**: Minor artifacts remain
- **Poor (5+ detections)**: Significant VM fingerprints detectable

## Performance Considerations

### System Impact
- **CPU Usage**: Minimal during execution
- **Memory Usage**: Low footprint
- **Disk I/O**: Moderate during file operations
- **Network**: Brief interruption during MAC changes

### VM Performance Effects
- **Graphics**: May be reduced due to 3D driver modifications
- **Network**: Temporary disconnection during adapter changes
- **Input**: Possible mouse/keyboard lag after driver changes
- **File Sharing**: Host-guest file sharing disabled

## Error Handling & Recovery

### Common Issues
1. **Access Denied**: Insufficient privileges (need SYSTEM)
2. **Registry Errors**: Keys may not exist on all systems
3. **File Lock Errors**: Files in use cannot be renamed
4. **Network Disruption**: Adapter changes may cause connectivity loss

### Recovery Mechanisms
- **VM Snapshot**: Primary recovery method
- **Registry Backup**: Automatic backup of modified keys
- **Service Restoration**: Some services can be restarted
- **Network Reset**: Adapter disable/enable cycle

## Security Implications

### Defensive Considerations
- Changes make VM less detectable to malware
- May interfere with legitimate VM management tools
- Some modifications are irreversible without restoration
- Network security tools may flag MAC address changes

### Operational Security
- Always use in isolated environments
- Monitor for unexpected system behavior
- Validate changes before malware deployment
- Maintain clean snapshots for recovery

## Future Enhancements

### Planned Improvements
1. **CPUID Manipulation**: Kernel-level hypervisor bit hiding
2. **Timing Attack Mitigation**: RDTSC instruction normalization
3. **Memory Artifact Hiding**: IDT/GDT location spoofing
4. **ACPI Table Modification**: System firmware table editing
5. **Hardware Simulation**: Realistic thermal/power management

### Research Areas
- Machine learning-based detection evasion
- Dynamic adaptation to new detection techniques
- Automated testing against evolving malware
- Integration with malware analysis frameworks

---

**Document Version**: 1.0  
**Last Updated**: 2024  
**Compatibility**: VMwareCloak Enhanced v0.5
