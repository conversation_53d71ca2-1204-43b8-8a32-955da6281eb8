# VMwareCloak Enhanced v0.5

A comprehensive PowerShell script designed to help malware analysts hide their virtualized Windows environments from sophisticated malware detection systems. This enhanced version significantly expands upon the original VMwareCloak with modern evasion techniques targeting 2023-2024 malware detection methods.

## 🚀 What's New in Enhanced Version

### Comprehensive Detection Evasion
- **Multi-Hypervisor Support**: VMware, VirtualBox, Hyper-V, QEMU/KVM
- **MAC Address Spoofing**: Changes VM MAC addresses to legitimate vendor patterns
- **WMI Data Manipulation**: Modifies BIOS and hardware information that WMI queries expose
- **Hardware Device Management**: Hides/disables virtual hardware devices
- **Network Adapter Modifications**: Changes adapter names and descriptions
- **Enhanced Registry Coverage**: Targets additional VM detection vectors
- **Validation Framework**: Built-in testing against common detection techniques

### Original Functionality (Enhanced)
- Registry key modifications (expanded coverage)
- VMware process termination (multi-hypervisor support)
- File system modifications (broader scope)
- Driver file management (additional virtual drivers)

## 🎯 Detection Techniques Addressed

### Hardware Fingerprinting
- ✅ MAC address patterns (VMware: 00:05:69, 00:0C:29, 00:1C:14, 00:50:56)
- ✅ Network adapter names and descriptions
- ✅ Virtual hardware device enumeration
- ✅ System manufacturer and model information
- ✅ BIOS vendor and version strings
- ✅ Processor identification strings

### Registry Artifacts
- ✅ SCSI device identifiers (multiple ports and buses)
- ✅ System information keys (manufacturer, product name)
- ✅ BIOS information registry entries
- ✅ VMware-specific service and application keys
- ✅ Network adapter configuration entries
- ✅ Virtual device driver registry keys

### Process and Service Detection
- ✅ VMware Tools processes (vmtoolsd, vmwaretray, etc.)
- ✅ VirtualBox processes (VBoxService, VBoxTray)
- ✅ Hyper-V integration services
- ✅ QEMU guest agent processes

### File System Artifacts
- ✅ VMware driver files (vmhgfs.sys, vmmemctl.sys, etc.)
- ✅ VirtualBox driver files (VBoxGuest.sys, VBoxMouse.sys, etc.)
- ✅ Virtual machine tool executables
- ✅ Guest addition installations

### WMI Query Results
- ✅ Win32_Bios information (SerialNumber, Vendor, Version)
- ✅ Win32_ComputerSystem (Model, Manufacturer)
- ✅ Win32_Processor (Name, ProcessorId)
- ✅ Win32_NetworkAdapterConfiguration (MACAddress)
- ✅ Hardware device enumeration results

## ⚠️ Limitations & External Requirements

### Techniques Requiring External Tools
Some advanced detection methods cannot be addressed with PowerShell alone:

#### CPUID-Based Detection
- **Issue**: Hypervisor presence bit (CPUID EAX=1, ECX bit 31)
- **Solution**: Hypervisor configuration changes
  - VMware: Add `hypervisor.cpuid.v0 = "FALSE"` to .vmx file
  - VirtualBox: `VBoxManage modifyvm <vm> --paravirtprovider none`

#### Timing Attacks (RDTSC)
- **Issue**: VM exit overhead detection via RDTSC instruction
- **Solution**: Hypervisor-level timing normalization
  - VMware: Configure `rdtsc.offset` settings
  - Requires kernel-level timing manipulation tools

#### Memory Artifacts
- **Issue**: IDT/GDT/LDT location detection
- **Solution**: Kernel-level memory layout spoofing
  - Requires ring-0 drivers or specialized tools
  - Memory layout randomization utilities

#### ACPI/SMBIOS Tables
- **Issue**: System firmware table inspection
- **Solution**: BIOS-level modifications
  - ACPI table editors
  - System firmware patching tools

## 🔧 Installation & Usage

### System Requirements
- **Operating System**: Windows 7, 8, 10, 11 (x86/x64)
- **PowerShell**: Version 3.0 or higher
- **Privileges**: SYSTEM level access (Administrator insufficient for full effectiveness)
- **Environment**: Virtual machine only (VMware, VirtualBox, Hyper-V, QEMU)

### Getting SYSTEM Privileges

#### Method 1: Process Hacker (Recommended)
1. Download and install [Process Hacker](https://processhacker.sourceforge.io/)
2. Start PowerShell as Administrator
3. Open Process Hacker as Administrator
4. Find `powershell.exe` in the process list
5. Right-click → "Miscellaneous" → "Run As"
6. Select "System" from the dropdown
7. Click OK - this spawns a SYSTEM PowerShell

#### Method 2: PsExec
```cmd
# Download PsExec from Microsoft Sysinternals
psexec -i -s powershell.exe
```

#### Method 3: Task Scheduler
```cmd
# Create a task that runs as SYSTEM
schtasks /create /tn "SystemPS" /tr "powershell.exe" /sc once /st 00:00 /ru SYSTEM
schtasks /run /tn "SystemPS"
```

### Basic Usage Examples

#### Complete Evasion (Recommended)
```powershell
# Apply all evasion techniques
.\VMwareCloak.ps1 -all
```

#### Selective Evasion
```powershell
# Original functionality only
.\VMwareCloak.ps1 -reg -procs -files

# Modern enhancements only
.\VMwareCloak.ps1 -mac -wmi -hardware -network

# Multi-hypervisor support
.\VMwareCloak.ps1 -multi

# Validate effectiveness
.\VMwareCloak.ps1 -validate
```

#### Custom Combinations
```powershell
# Registry and MAC spoofing only
.\VMwareCloak.ps1 -reg -mac

# Full VMware + VirtualBox evasion
.\VMwareCloak.ps1 -all -multi

# Quick validation after changes
.\VMwareCloak.ps1 -validate
```

### Command Line Options

| Option | Description | Scope |
|--------|-------------|-------|
| `-all` | Enable all evasion techniques | Complete |
| `-reg` | Registry modifications | Original + Enhanced |
| `-procs` | Process termination | Multi-hypervisor |
| `-files` | File system modifications | Enhanced coverage |
| `-mac` | MAC address spoofing | Network layer |
| `-wmi` | WMI data source modifications | Hardware info |
| `-hardware` | Virtual device management | Device layer |
| `-network` | Network adapter modifications | Network layer |
| `-multi` | Multi-hypervisor evasion | VBox, Hyper-V, QEMU |
| `-validate` | Run detection validation | Testing |
| `-help` | Show detailed help | Information |

### Execution Workflow

1. **Pre-Execution**
   - Create VM snapshot for rollback
   - Ensure SYSTEM privileges
   - Close unnecessary applications

2. **Execution**
   ```powershell
   .\VMwareCloak.ps1 -all
   ```

3. **Validation**
   ```powershell
   .\VMwareCloak.ps1 -validate
   ```

4. **Malware Analysis**
   - Deploy and execute malware samples
   - Monitor for evasion effectiveness

5. **Post-Analysis**
   - Revert VM to clean snapshot
   - Document findings

## 🧪 Testing & Validation

### Detection Tool Testing
Test the effectiveness of evasion techniques using these tools:

#### Pafish (Paranoid Fish)
```bash
# Download and run pafish to test VM detection
# https://github.com/a0rtega/pafish
pafish.exe
```

#### Al-Khaser
```bash
# Comprehensive malware analysis evasion testing
# https://github.com/ayoubfaouzi/al-khaser
al-khaser.exe --check VMWARE --check VBOX --check TIMING_ATTACKS
```

#### Custom Validation
The script includes built-in validation:
```powershell
.\VMwareCloak.ps1 -validate
```

### Expected Results
- **Before Enhancement**: 15-20 detections in pafish/al-khaser
- **After Enhancement**: 3-5 detections (primarily CPUID/timing-based)
- **With External Tools**: 0-2 detections

## 🛠️ Advanced Configuration

### Hypervisor-Level Modifications

#### VMware Workstation/Player
Add these lines to your `.vmx` file:
```ini
# Disable hypervisor CPUID bit
hypervisor.cpuid.v0 = "FALSE"

# Timing attack mitigation
rdtsc.offset = "0"

# Hide VM from guest
isolation.tools.unity.disable = "TRUE"
isolation.tools.unityInterlockOperation.disable = "TRUE"
isolation.tools.unity.push.enable = "FALSE"
isolation.tools.unity.taskbar.enable = "FALSE"

# Additional stealth settings
gui.restricted = "TRUE"
isolation.tools.hgfs.disable = "TRUE"
isolation.tools.dnd.disable = "TRUE"
isolation.tools.copy.enable = "FALSE"
isolation.tools.paste.enabled = "FALSE"
```

#### VirtualBox
```bash
# Disable paravirtualization
VBoxManage modifyvm "YourVM" --paravirtprovider none

# Hide hypervisor from guest
VBoxManage modifyvm "YourVM" --cpuidset 00000001 000106e5 00100800 0098e3fd bfebfbff

# Disable guest additions
VBoxManage modifyvm "YourVM" --biosbootmenu disabled
```

#### Hyper-V
```powershell
# Disable Hyper-V enlightenments
Set-VMProcessor -VMName "YourVM" -ExposeVirtualizationExtensions $false
```

### Third-Party Tools for Complete Evasion

#### InvisiMem (CPUID Hooking)
- **Purpose**: Hook CPUID instruction to hide hypervisor presence
- **Download**: Research-only tool, limited availability
- **Usage**: Kernel driver installation required

#### VMware Stealth Patches
- **Purpose**: Patch VMware binaries to reduce fingerprints
- **Availability**: Community-developed patches
- **Risk**: May affect VM stability

#### Hardware Simulation Tools
- **Purpose**: Simulate realistic hardware configurations
- **Examples**: Custom ACPI tables, SMBIOS modifications
- **Complexity**: Requires advanced system knowledge

## ⚖️ Legal & Ethical Considerations

### Legitimate Use Cases
- **Malware Analysis**: Security research in controlled environments
- **Penetration Testing**: Red team exercises and security assessments
- **Academic Research**: Computer security and malware behavior studies
- **Incident Response**: Analysis of suspicious files and behaviors

### Legal Compliance
- ✅ Use only in isolated virtual environments
- ✅ Obtain proper authorization for security testing
- ✅ Follow responsible disclosure practices
- ✅ Comply with local and international laws
- ❌ Do not use for malicious purposes
- ❌ Do not deploy on production systems
- ❌ Do not distribute malware or facilitate attacks

### Ethical Guidelines
1. **Purpose**: Use only for defensive security research
2. **Environment**: Isolated virtual machines only
3. **Data**: Do not process sensitive or personal information
4. **Disclosure**: Report vulnerabilities responsibly
5. **Education**: Share knowledge to improve security

## 🚨 Warnings & Disclaimers

### Critical Warnings
- ⚠️ **VM ONLY**: Never run on physical host systems
- ⚠️ **SNAPSHOT FIRST**: Always create VM snapshots before execution
- ⚠️ **SYSTEM PRIVILEGES**: Requires SYSTEM-level access for full effectiveness
- ⚠️ **PERFORMANCE IMPACT**: May reduce VM performance due to driver modifications
- ⚠️ **IRREVERSIBLE CHANGES**: Some modifications require VM restoration to undo

### Known Limitations
- Cannot address all detection vectors (CPUID, timing attacks)
- May not work against custom or proprietary detection methods
- Effectiveness varies by malware sophistication
- Some changes require VM restart to take effect

### Troubleshooting
- **Access Denied Errors**: Ensure running as SYSTEM, not just Administrator
- **Network Issues**: MAC address changes may require adapter restart
- **Performance Problems**: Revert to snapshot if VM becomes unstable
- **Detection Still Occurring**: Use validation mode to identify remaining artifacts

## 🤝 Contributions & Acknowledgments

### Original Contributors
- **Kyle Cucci (@d4rksystem)**: Original VMwareCloak author
- **Takashi Matsumoto (@t_mtsmt)**: Feature additions and bug fixes
- **MMJv3**: Registry key enhancements

### Enhanced Version Contributors
- **Enhanced by**: Security research community
- **Inspiration**: al-khaser, pafish, and modern malware analysis techniques
- **Testing**: Various malware analysis frameworks and detection tools

### Contributing
Contributions are welcome! Please:
1. Fork the repository
2. Create feature branches for new evasion techniques
3. Test thoroughly in isolated environments
4. Submit pull requests with detailed descriptions
5. Follow responsible disclosure for any vulnerabilities found

### References & Research
- [Pafish - Paranoid Fish VM Detection](https://github.com/a0rtega/pafish)
- [Al-Khaser - Anti-Analysis Techniques](https://github.com/ayoubfaouzi/al-khaser)
- [VMware Security Advisories](https://www.vmware.com/security/advisories.html)
- [VirtualBox Security Documentation](https://www.virtualbox.org/wiki/Security)
- [MITRE ATT&CK - Virtualization/Sandbox Evasion](https://attack.mitre.org/techniques/T1497/)

---

**Version**: Enhanced v0.5
**Last Updated**: 2024
**License**: GPL-2.0
**Status**: Active Development

For issues, suggestions, or contributions, please visit the project repository or contact the maintainers through appropriate security research channels.

