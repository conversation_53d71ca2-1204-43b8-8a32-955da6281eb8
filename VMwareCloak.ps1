#################################################
## VMwareCloak.ps1: A script that attempts to hide the VMware Workstation hypervisor from malware by modifying registry keys, killing associated processes, and removing uneeded driver/system files.
## Written and tested on Windows 7 and Windows 10. Should work for Windows 11 as well!
## Many thanks to pafish for some of the ideas - https://github.com/a0rtega/pafish
##################################################
## Author: d4rksystem (<PERSON>)
## Version: 0.4
##################################################

# Define command line parameters
param (
    [switch]$all = $false,
    [switch]$reg = $false,
    [switch]$procs = $false,
    [switch]$files = $false,
    [switch]$mac = $false,
    [switch]$wmi = $false,
    [switch]$hardware = $false,
    [switch]$network = $false,
    [switch]$multi = $false,
    [switch]$validate = $false,
    [switch]$help = $false
)

if ($all) {
    $reg = $true
    $procs = $true
    $files = $true
    $mac = $true
    $wmi = $true
    $hardware = $true
    $network = $true
    $multi = $true
}

if ($help) {
    Write-Output ""
    Write-Output "VMwareCloak.ps1 Enhanced v0.5 by @d4rksystem (Kyle Cucci)"
    Write-Output "Enhanced for comprehensive VM detection evasion"
    Write-Output ""
    Write-Output "Usage: VMwareCloak.ps1 -<option>"
    Write-Output "Example Usage: VMwareCloak.ps1 -all"
    Write-Output ""
    Write-Output "Options:"
    Write-Output "  -all       Enable all evasion techniques"
    Write-Output "  -reg       Make registry changes (original functionality)"
    Write-Output "  -procs     Kill VMware processes (original functionality)"
    Write-Output "  -files     Make file system changes (original functionality)"
    Write-Output "  -mac       Spoof MAC addresses to non-VM patterns"
    Write-Output "  -wmi       Modify WMI data sources (BIOS, hardware info)"
    Write-Output "  -hardware  Hide/disable virtual hardware devices"
    Write-Output "  -network   Modify network adapter names and descriptions"
    Write-Output "  -multi     Enable multi-hypervisor evasion (VBox, Hyper-V, QEMU)"
    Write-Output "  -validate  Run validation checks against detection tools"
    Write-Output "  -help      Show this help message"
    Write-Output ""
    Write-Output "Requirements:"
    Write-Output "  - Must run as SYSTEM (not just Administrator)"
    Write-Output "  - Windows 7/8/10/11 supported"
    Write-Output "  - VM snapshot recommended before execution"
    Write-Output ""
    Write-Output "Warning: Only run in a virtual machine for malware analysis!"
    Write-Output ""
    exit
}

# Check privileges and display banner
if (-not (Test-AdminPrivileges)) {
    Write-Status "ERROR: This script requires Administrator privileges!" "ERROR"
    Write-Output "Please run PowerShell as Administrator and try again."
    exit 1
}

if (-not (Test-SystemPrivileges)) {
    Write-Status "WARNING: Running as Administrator, but SYSTEM privileges recommended for best results!" "WARNING"
    Write-Output "For maximum effectiveness, run as SYSTEM using Process Hacker or similar tool."
    Write-Output ""
}

# Display banner
Write-Output ""
Write-Output "╔══════════════════════════════════════════════════════════════════════════════╗"
Write-Output "║                    VMwareCloak Enhanced v0.5                                ║"
Write-Output "║              Comprehensive VM Detection Evasion Tool                        ║"
Write-Output "║                                                                              ║"
Write-Output "║  Original by: @d4rksystem (Kyle Cucci)                                      ║"
Write-Output "║  Enhanced for: Modern malware analysis environments (2024)                  ║"
Write-Output "║  Supports: VMware, VirtualBox, Hyper-V, QEMU/KVM evasion                   ║"
Write-Output "╚══════════════════════════════════════════════════════════════════════════════╝"
Write-Output ""

if (-not ($all -or $reg -or $procs -or $files -or $mac -or $wmi -or $hardware -or $network -or $multi -or $validate)) {
    Write-Output "Usage: VMwareCloak.ps1 -<option>"
    Write-Output "Example: VMwareCloak.ps1 -all"
    Write-Output ""
    Write-Output "Quick Options:"
    Write-Output "  -all       Enable all evasion techniques (recommended)"
    Write-Output "  -help      Show detailed help information"
    Write-Output ""
    Write-Output "Individual Options:"
    Write-Output "  -reg       Registry modifications (original + enhanced)"
    Write-Output "  -procs     Process termination (VMware + multi-hypervisor)"
    Write-Output "  -files     File system modifications (enhanced coverage)"
    Write-Output "  -mac       MAC address spoofing to legitimate patterns"
    Write-Output "  -wmi       WMI data source modifications"
    Write-Output "  -hardware  Virtual hardware device management"
    Write-Output "  -network   Network adapter modifications"
    Write-Output "  -multi     Multi-hypervisor evasion techniques"
    Write-Output "  -validate  Run detection validation checks"
    Write-Output ""
    Write-Output "WARNING: Only run in a virtual machine for malware analysis!"
    Write-Output "REQUIREMENT: Must run as SYSTEM for full effectiveness!"
    Write-Output ""
    exit
}

# -------------------------------------------------------------------------------------------------------
# Define utility functions

function Get-RandomString {
    param([int]$Length = 10)
    $charSet = "abcdefghijklmnopqrstuvwxyz0123456789".ToCharArray()
    $randomString = ""
    for ($i = 0; $i -lt $Length; $i++ ) {
        $randomString += $charSet | Get-Random
    }
    return $randomString
}

function Get-RandomMacAddress {
    # Generate realistic MAC addresses from common legitimate vendors
    $vendorPrefixes = @(
        "00:1B:21",  # Intel
        "00:E0:4C",  # Realtek
        "00:26:B9",  # Intel
        "00:23:24",  # Intel
        "00:1F:3C",  # Intel
        "00:19:D1",  # Intel
        "00:15:17",  # Intel
        "00:13:02",  # Intel
        "00:16:EA",  # Intel
        "00:21:70"   # Intel
    )

    $prefix = $vendorPrefixes | Get-Random
    $suffix = "{0:X2}:{1:X2}:{2:X2}" -f (Get-Random -Maximum 256), (Get-Random -Maximum 256), (Get-Random -Maximum 256)
    return "${prefix}:${suffix}"
}

function Write-Status {
    param([string]$Message, [string]$Type = "INFO")
    $timestamp = Get-Date -Format "HH:mm:ss"
    switch ($Type) {
        "SUCCESS" { Write-Host "[$timestamp] [+] $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "[$timestamp] [-] $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "[$timestamp] [!] $Message" -ForegroundColor Yellow }
        default   { Write-Host "[$timestamp] [*] $Message" -ForegroundColor Cyan }
    }
}

function Test-AdminPrivileges {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Test-SystemPrivileges {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    return $currentUser.Name -eq "NT AUTHORITY\SYSTEM"
}

# -------------------------------------------------------------------------------------------------------
# MAC Address Spoofing

if ($mac) {
    Write-Status "Starting MAC address spoofing..." "INFO"

    try {
        # Get all network adapters
        $adapters = Get-NetAdapter | Where-Object { $_.Status -eq "Up" -or $_.Status -eq "Disconnected" }

        foreach ($adapter in $adapters) {
            $currentMac = $adapter.MacAddress

            # Check if current MAC is VM-related
            $vmMacPrefixes = @("00:05:69", "00:0C:29", "00:1C:14", "00:50:56", "08:00:27", "00:1C:42", "00:16:3E", "0A:00:27")
            $isVmMac = $false

            foreach ($prefix in $vmMacPrefixes) {
                if ($currentMac.StartsWith($prefix.Replace(":", "-"))) {
                    $isVmMac = $true
                    break
                }
            }

            if ($isVmMac -or $adapter.Name -like "*VMware*" -or $adapter.Name -like "*VirtualBox*") {
                $newMac = Get-RandomMacAddress
                $newMacFormatted = $newMac.Replace(":", "")

                Write-Status "Spoofing MAC for adapter '$($adapter.Name)': $currentMac -> $newMac" "INFO"

                # Method 1: Try using registry modification
                $adapterGuid = $adapter.InterfaceGuid
                $regPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e972-e325-11ce-bfc1-08002be10318}"

                # Find the adapter's registry key
                $subKeys = Get-ChildItem -Path $regPath -ErrorAction SilentlyContinue
                foreach ($subKey in $subKeys) {
                    $netCfgInstanceId = Get-ItemProperty -Path $subKey.PSPath -Name "NetCfgInstanceId" -ErrorAction SilentlyContinue
                    if ($netCfgInstanceId -and $netCfgInstanceId.NetCfgInstanceId -eq $adapterGuid) {
                        try {
                            Set-ItemProperty -Path $subKey.PSPath -Name "NetworkAddress" -Value $newMacFormatted -ErrorAction Stop
                            Write-Status "Successfully set MAC address in registry for $($adapter.Name)" "SUCCESS"
                        } catch {
                            Write-Status "Failed to set MAC in registry for $($adapter.Name): $($_.Exception.Message)" "ERROR"
                        }
                        break
                    }
                }

                # Method 2: Try using netsh (as backup)
                try {
                    $result = netsh interface set interface name="$($adapter.Name)" admin=disable 2>&1
                    Start-Sleep -Seconds 2
                    $result = netsh interface set interface name="$($adapter.Name)" admin=enable 2>&1
                    Write-Status "Reset network adapter $($adapter.Name)" "INFO"
                } catch {
                    Write-Status "Could not reset adapter $($adapter.Name)" "WARNING"
                }
            }
        }
    } catch {
        Write-Status "Error during MAC address spoofing: $($_.Exception.Message)" "ERROR"
    }
}

# -------------------------------------------------------------------------------------------------------
# Stop VMware Processes

$process_list = "vmtoolsd", "vm3dservice", "VGAuthService", "VMwareService", "Vmwaretray", "Vmwareuser", "TPAutoConnSvc"

if ($procs) {

    Write-Output '[*] Attempting to kill VMware processes...'

    foreach ($p in $process_list) {

        $process = Get-Process "$p" -ErrorAction SilentlyContinue

        if ($process) {
            $process | Stop-Process -Force
            Write-Output "[*] $p process killed!"
        }

        if (!$process) {
            Write-Output "[!] $p process does not exist!"
        }
     }        
}

# -------------------------------------------------------------------------------------------------------
# Modify VMware registry keys

if ($reg) {

   # Remove or rename VMware-related registry keys

    if (Get-ItemProperty -Path "HKLM:\HARDWARE\DEVICEMAP\Scsi\Scsi Port 3\Scsi Bus 1\Target Id 0\Logical Unit Id 0\" -Name "Identifier" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Renaming Reg Key HKLM:\HARDWARE\DEVICEMAP\Scsi\Scsi Port 3\Scsi Bus 1\Target Id 0\Logical Unit Id 0\Identifier"
        Set-ItemProperty -Path "HKLM:\HARDWARE\DEVICEMAP\Scsi\Scsi Port 3\Scsi Bus 1\Target Id 0\Logical Unit Id 0\" -Name "Identifier" -Value  $(Get-RandomString)

     } Else {

        Write-Output '[!] Reg Key HKLM:\HARDWARE\DEVICEMAP\Scsi\Scsi Port 3\Scsi Bus 1\Target Id 0\Logical Unit Id 0\Identifier" does not seem to exist! Skipping this one...'
    }

    if (Get-ItemProperty -Path "HKLM:\SYSTEM\ControlSet001\Control\Class\{4d36e97d-e325-11ce-bfc1-08002be10318}\0133\" -Name "DriverDesc" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Renaming Reg Key HKLM:\SYSTEM\ControlSet001\Control\Class\{4d36e97d-e325-11ce-bfc1-08002be10318}\0133\DriverDesc"
        Set-ItemProperty -Path "HKLM:\SYSTEM\ControlSet001\Control\Class\{4d36e97d-e325-11ce-bfc1-08002be10318}\0133\" -Name "DriverDesc" -Value  $(Get-RandomString)

     } Else {

        Write-Output '[!] Reg Key HKLM:\SYSTEM\ControlSet001\Control\Class\{4d36e97d-e325-11ce-bfc1-08002be10318}\0133\DriverDesc does not seem to exist! Skipping this one...'
    }

    if (Get-ItemProperty -Path "HKLM:\SYSTEM\ControlSet001\Control\Class\{4d36e97d-e325-11ce-bfc1-08002be10318}\0133\" -Name "InfSection" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Renaming Reg Key HKLM:SYSTEM\ControlSet001\Control\Class\{4d36e97d-e325-11ce-bfc1-08002be10318}\0133\InfSection"
        Set-ItemProperty -Path "HKLM:\SYSTEM\ControlSet001\Control\Class\{4d36e97d-e325-11ce-bfc1-08002be10318}\0133\" -Name "InfSection" -Value  $(Get-RandomString)

     } Else {

        Write-Output '[!] Reg Key HKLM:\SYSTEM\ControlSet001\Control\Class\{4d36e97d-e325-11ce-bfc1-08002be10318}\0133\InfSection does not seem to exist! Skipping this one...'
    }

    if (Get-ItemProperty -Path "HKLM:\HARDWARE\DESCRIPTION\System" -Name "SystemBiosVersion" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Renaming Reg Key HKLM:\HARDWARE\DESCRIPTION\System\SystemBiosVersion..."
        Set-ItemProperty -Path "HKLM:\HARDWARE\DESCRIPTION\System" -Name "SystemBiosVersion" -Value  $(Get-RandomString)

     } Else {

        Write-Output '[!] Reg Key HKLM:\HARDWARE\DESCRIPTION\System\SystemBiosVersion does not seem to exist! Skipping this one...'
    }

    if (Get-ItemProperty -Path "HKLM:\HARDWARE\DESCRIPTION\System\BIOS" -Name "BIOSVendor" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Renaming Reg Key HKLM:\HARDWARE\DESCRIPTION\System\BIOS\BIOSVendor..."
	Set-ItemProperty -Path "HKLM:\HARDWARE\DESCRIPTION\System\BIOS" -Name "BIOSVendor" -Value "American Megatrends International, LLC."

     } Else {

        Write-Output '[!] Reg Key HKLM:\HARDWARE\DESCRIPTION\System\BIOS\BIOSVendor does not seem to exist! Skipping this one...'
    }

    if (Get-ItemProperty -Path "HKLM:\HARDWARE\DESCRIPTION\System\BIOS" -Name "BIOSVersion" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Renaming Reg Key HKLM:\HARDWARE\DESCRIPTION\System\BIOS\BIOSVersion..."
        Set-ItemProperty -Path "HKLM:\HARDWARE\DESCRIPTION\System\BIOS" -Name "BIOSVersion" -Value  1.70

     } Else {

        Write-Output '[!] Reg Key HKLM:\HARDWARE\DESCRIPTION\System\BIOS\BIOSVersion does not seem to exist! Skipping this one...'
    }

    if (Get-ItemProperty -Path "HKLM:\HARDWARE\DESCRIPTION\System\BIOS" -Name "SystemManufacturer" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Renaming Reg Key HKLM:\HARDWARE\DESCRIPTION\System\BIOS\SystemManufacturer..."
        Set-ItemProperty -Path "HKLM:\HARDWARE\DESCRIPTION\System\BIOS" -Name "SystemManufacturer" -Value  $(Get-RandomString)

     } Else {

        Write-Output '[!] Reg Key HKLM:\HARDWARE\DESCRIPTION\System\BIOS\SystemManufacturer does not seem to exist! Skipping this one...'
    }
	
	if (Get-ItemProperty -Path "HKLM:\HARDWARE\DESCRIPTION\System\BIOS" -Name "SystemProductName" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Renaming Reg Key HKLM:\HARDWARE\DESCRIPTION\System\BIOS\SystemProductName..."
        Set-ItemProperty -Path "HKLM:\HARDWARE\DESCRIPTION\System\BIOS" -Name "SystemProductName" -Value  $(Get-RandomString)

     } Else {

        Write-Output '[!] Reg Key HKLM:\HARDWARE\DESCRIPTION\System\BIOS\SystemProductName does not seem to exist! Skipping this one...'
    }
	
	if (Get-ItemProperty -Path "HKLM:\HARDWARE\DEVICEMAP\Scsi\Scsi Port 0\Scsi Bus 0\Target Id 0\Logical Unit Id 0" -Name "Identifier" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Renaming Reg Key HKLM:\HARDWARE\DEVICEMAP\Scsi\Scsi Port 0\Scsi Bus 0\Target Id 0\Logical Unit Id 0\Identifier..."
        Set-ItemProperty -Path "HKLM:\HARDWARE\DEVICEMAP\Scsi\Scsi Port 0\Scsi Bus 0\Target Id 0\Logical Unit Id 0" -Name "Identifier" -Value  $(Get-RandomString)

     } Else {

        Write-Output '[!] Reg Key HKLM:\HARDWARE\DEVICEMAP\Scsi\Scsi Port 0\Scsi Bus 0\Target Id 0\Logical Unit Id 0\Identifier does not seem to exist! Skipping this one...'
    }

	if (Get-ItemProperty -Path "HKLM:\HARDWARE\DEVICEMAP\Scsi\Scsi Port 1\Scsi Bus 0\Target Id 0\Logical Unit Id 0" -Name "Identifier" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Renaming Reg Key HKLM:\HARDWARE\DEVICEMAP\Scsi\Scsi Port 1\Scsi Bus 0\Target Id 0\Logical Unit Id 0\Identifier..."
        Set-ItemProperty -Path "HKLM:\HARDWARE\DEVICEMAP\Scsi\Scsi Port 1\Scsi Bus 0\Target Id 0\Logical Unit Id 0" -Name "Identifier" -Value  $(Get-RandomString)

     } Else {

        Write-Output '[!] Reg Key HKLM:\HARDWARE\DEVICEMAP\Scsi\Scsi Port 1\Scsi Bus 0\Target Id 0\Logical Unit Id 0\Identifier does not seem to exist! Skipping this one...'
    }

	if (Get-ItemProperty -Path "HKLM:\HARDWARE\DEVICEMAP\Scsi\Scsi Port 2\Scsi Bus 0\Target Id 0\Logical Unit Id 0" -Name "Identifier" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Renaming Reg Key HKLM:\HARDWARE\DEVICEMAP\Scsi\Scsi Port 2\Scsi Bus 0\Target Id 0\Logical Unit Id 0\Identifier..."
        Set-ItemProperty -Path "HKLM:\HARDWARE\DEVICEMAP\Scsi\Scsi Port 2\Scsi Bus 0\Target Id 0\Logical Unit Id 0" -Name "Identifier" -Value  $(Get-RandomString)

     } Else {

        Write-Output '[!] Reg Key HKLM:\HARDWARE\DEVICEMAP\Scsi\Scsi Port 2\Scsi Bus 0\Target Id 0\Logical Unit Id 0\Identifier does not seem to exist! Skipping this one...'
    }
	
	if (Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\WinSAT" -Name "PrimaryAdapterString" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Renaming Reg Key HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\WinSAT\PrimaryAdapterString..."
        Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\WinSAT\" -Name "PrimaryAdapterString" -Value  $(Get-RandomString)

     } Else {

        Write-Output '[!] Reg Key HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\WinSAT\PrimaryAdapterString does not seem to exist! Skipping this one...'
    }
	
	if (Get-ItemProperty -Path "HKLM:\SYSTEM\ControlSet001\Control\SystemInformation" -Name "SystemManufacturer" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Renaming Reg Key HKLM:\SYSTEM\ControlSet001\Control\SystemInformation\SystemManufacturer..."
        Set-ItemProperty -Path "HKLM:\SYSTEM\ControlSet001\Control\SystemInformation" -Name "SystemManufacturer" -Value  $(Get-RandomString)

     } Else {

        Write-Output '[!] Reg Key HKLM:\SYSTEM\ControlSet001\Control\SystemInformation\SystemManufacturer does not seem to exist! Skipping this one...'
    }
	
	if (Get-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation" -Name "SystemManufacturer" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Renaming Reg Key HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation\SystemManufacturer..."
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation" -Name "SystemManufacturer" -Value  $(Get-RandomString)

     } Else {

        Write-Output '[!] Reg Key HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation\SystemManufacturer does not seem to exist! Skipping this one...'
    }
	
	if (Get-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation" -Name "SystemProductName" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Renaming Reg Key HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation\SystemProductName..."
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation" -Name "SystemProductName" -Value  $(Get-RandomString)

     } Else {

        Write-Output '[!] Reg Key HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation\SystemProductName does not seem to exist! Skipping this one...'
    }

	if (Get-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\disk\Enum" -Name "0" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Renaming Reg Key HKLM:\SYSTEM\CurrentControlSet\Services\disk\Enum\0..."
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\disk\Enum" -Name "0" -Value  $(Get-RandomString)

     } Else {

        Write-Output '[!] Reg Key HKLM:\SYSTEM\CurrentControlSet\Services\disk\Enum\0 does not seem to exist! Skipping this one...'
    }	
	
	if (Get-ItemProperty -Path "HKLM:\SYSTEM\ControlSet001\Control\SystemInformation" -Name "SystemProductName" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Renaming Reg Key HKLM:\SYSTEM\ControlSet001\Control\SystemInformation\SystemProductName..."
        Set-ItemProperty -Path "HKLM:\SYSTEM\ControlSet001\Control\SystemInformation" -Name "SystemProductName" -Value  $(Get-RandomString)

     } Else {

        Write-Output '[!] Reg Key HKLM:\SYSTEM\ControlSet001\Control\SystemInformation\SystemProductName does not seem to exist! Skipping this one...'
    }
		
   if (Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" -Name "VMware User Process" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Removing Reg Key HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run\VMware User Process..."
        Remove-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" -Name "VMware User Process"

     } Else {

        Write-Output '[!] Reg Key HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run\VMware User Process does not seem to exist! Skipping this one...'
    }

    if (Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" -Name "VMware VM3DService Process" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Removing Reg Key HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run\VMware VM3DService Process..."
        Remove-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" -Name "VMware VM3DService Process"

     } Else {

        Write-Output '[!] Reg Key HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run\VMware VM3DService Process does not seem to exist! Skipping this one...'
    }

    if (Get-ItemProperty -Path "HKLM:\SOFTWARE\RegisteredApplications" -Name "VMware Host Open" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Removing Reg Key HKLM:\SOFTWARE\RegisteredApplications\VMware Host Open"
        Remove-ItemProperty -Path "HKLM:\SOFTWARE\RegisteredApplications" -Name "VMware Host Open"

    } Else {

        Write-Output '[!] Reg Key HKLM:\SOFTWARE\RegisteredApplications\VMware Host Open does not seem to exist, or has already been renamed! Skipping this one...'
    }

    if (Get-ItemProperty -Path "HKLM:\SOFTWARE\WOW6432Node\RegisteredApplications" -Name "VMware Host Open" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Removing Reg Key HKLM:\SOFTWARE\WOW6432Node\RegisteredApplications\VMware Host Open"
        Remove-ItemProperty -Path "HKLM:\SOFTWARE\WOW6432Node\RegisteredApplications" -Name "VMware Host Open"

    } Else {

        Write-Output '[!] Reg Key HKLM:\SOFTWARE\WOW6432Node\RegisteredApplications\VMware Host Open does not seem to exist, or has already been renamed! Skipping this one...'
    }
	
	if (Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Store\Configuration" -Name "OEMID" -ErrorAction SilentlyContinue) {

	Write-Output "[*] Modifying Reg Key HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Store\Configuration\OEMID"
        Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Store\Configuration" -Name "OEMID" -Value $(Get-RandomString)

    } Else {

        Write-Output '[!] Reg Key HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Store\Configuration\OEMID does not seem to exist, or has already been renamed! Skipping this one...'
    }
	
	if (Get-Item -Path "HKLM:\SOFTWARE\VMware, Inc." -ErrorAction SilentlyContinue) {

        Write-Output "[*] Renaming Reg Key HKLM:\SOFTWARE\VMware, Inc."
        Rename-Item -Path "HKLM:\SOFTWARE\VMware, Inc." -NewName $(Get-RandomString)

    } Else {

        Write-Output '[!] Reg Key HKLM:\SOFTWARE\VMware, Inc. does not seem to exist, or has already been renamed! Skipping this one...'
    }
	
	if (Get-Item -Path "HKLM:\SOFTWARE\Classes\Applications\VMwareHostOpen.exe" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Modifying Reg Key HKLM:\SOFTWARE\Classes\Applications\VMwareHostOpen.exe"
        Rename-Item -Path "HKLM:\SOFTWARE\Classes\Applications\VMwareHostOpen.exe" -NewName $(Get-RandomString)

    } Else {

        Write-Output '[!] Reg Key HKLM:\SOFTWARE\Classes\Applications\VMwareHostOpen.exe does not seem to exist, or has already been renamed! Skipping this one...'
    }

    if (Get-Item -Path "HKLM:\SOFTWARE\Classes\VMwareHostOpen.AssocURL" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Modifying Reg Key HKLM:\SOFTWARE\Classes\VMwareHostOpen.AssocURL"
        Rename-Item -Path "HKLM:\SOFTWARE\Classes\VMwareHostOpen.AssocURL" -NewName $(Get-RandomString)

    } Else {

        Write-Output '[!] Reg Key HKLM:\SOFTWARE\Classes\VMwareHostOpen.AssocURL does not seem to exist, or has already been renamed! Skipping this one...'
    }

    if (Get-Item -Path "HKLM:\SOFTWARE\Classes\VMwareHostOpen.AssocFile" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Modifying Reg Key HKLM:\SOFTWARE\Classes\VMwareHostOpen.AssocFile"
        Rename-Item -Path "HKLM:\SOFTWARE\Classes\VMwareHostOpen.AssocFile" -NewName $(Get-RandomString)

    } Else {

        Write-Output '[!] Reg Key HKLM:\SOFTWARE\Classes\VMwareHostOpen.AssocFile does not seem to exist, or has already been renamed! Skipping this one...'
    }
	
	if (Get-Item -Path "HKLM:\SYSTEM\ControlSet001\Services\VGAuthService" -ErrorAction SilentlyContinue) {

        Write-Output "[*] Renaming Reg Key HKLM:\SYSTEM\ControlSet001\Services\VGAuthService..."
        Rename-Item -Path "HKLM:\SYSTEM\ControlSet001\Services\VGAuthService" -NewName $(Get-RandomString)

     } Else {

        Write-Output '[!] Reg Key HKLM:\SYSTEM\ControlSet001\Services\VGAuthService does not seem to exist! Skipping this one...'
    }
}

# -------------------------------------------------------------------------------------------------------
# WMI Data Source Modifications

if ($wmi) {
    Write-Status "Starting WMI data source modifications..." "INFO"

    # Modify BIOS information that WMI queries read from
    try {
        # Generate realistic system information
        $manufacturers = @("Dell Inc.", "HP", "Lenovo", "ASUS", "Acer", "MSI", "Gigabyte Technology Co., Ltd.")
        $models = @("OptiPlex 7090", "EliteDesk 800 G6", "ThinkCentre M720q", "PRIME B450M-A", "Aspire TC-895", "MAG B550M MORTAR")
        $biosVendors = @("American Megatrends Inc.", "Phoenix Technologies Ltd.", "Insyde Corp.")

        $newManufacturer = $manufacturers | Get-Random
        $newModel = $models | Get-Random
        $newBiosVendor = $biosVendors | Get-Random
        $newSerialNumber = Get-RandomString -Length 8
        $newBiosVersion = "$(Get-Random -Minimum 1 -Maximum 9).$(Get-Random -Minimum 10 -Maximum 99)"
        $newBiosDate = "$(Get-Random -Minimum 1 -Maximum 12)/$(Get-Random -Minimum 1 -Maximum 28)/$(Get-Random -Minimum 2020 -Maximum 2024)"

        # Modify registry keys that WMI reads from
        $regModifications = @(
            @{Path="HKLM:\HARDWARE\DESCRIPTION\System\BIOS"; Name="SystemManufacturer"; Value=$newManufacturer},
            @{Path="HKLM:\HARDWARE\DESCRIPTION\System\BIOS"; Name="SystemProductName"; Value=$newModel},
            @{Path="HKLM:\HARDWARE\DESCRIPTION\System\BIOS"; Name="BIOSVendor"; Value=$newBiosVendor},
            @{Path="HKLM:\HARDWARE\DESCRIPTION\System\BIOS"; Name="BIOSVersion"; Value=$newBiosVersion},
            @{Path="HKLM:\HARDWARE\DESCRIPTION\System\BIOS"; Name="BIOSReleaseDate"; Value=$newBiosDate},
            @{Path="HKLM:\HARDWARE\DESCRIPTION\System\BIOS"; Name="SystemSerialNumber"; Value=$newSerialNumber},
            @{Path="HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation"; Name="SystemManufacturer"; Value=$newManufacturer},
            @{Path="HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation"; Name="SystemProductName"; Value=$newModel},
            @{Path="HKLM:\SYSTEM\ControlSet001\Control\SystemInformation"; Name="SystemManufacturer"; Value=$newManufacturer},
            @{Path="HKLM:\SYSTEM\ControlSet001\Control\SystemInformation"; Name="SystemProductName"; Value=$newModel}
        )

        foreach ($mod in $regModifications) {
            try {
                if (Test-Path $mod.Path) {
                    Set-ItemProperty -Path $mod.Path -Name $mod.Name -Value $mod.Value -ErrorAction Stop
                    Write-Status "Modified $($mod.Path)\$($mod.Name) = $($mod.Value)" "SUCCESS"
                } else {
                    Write-Status "Registry path not found: $($mod.Path)" "WARNING"
                }
            } catch {
                Write-Status "Failed to modify $($mod.Path)\$($mod.Name): $($_.Exception.Message)" "ERROR"
            }
        }

        # Modify processor information
        try {
            $processorPath = "HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\0"
            if (Test-Path $processorPath) {
                $newProcessorName = "Intel(R) Core(TM) i7-10700K CPU @ 3.80GHz"
                Set-ItemProperty -Path $processorPath -Name "ProcessorNameString" -Value $newProcessorName -ErrorAction Stop
                Write-Status "Modified processor name to: $newProcessorName" "SUCCESS"
            }
        } catch {
            Write-Status "Failed to modify processor information: $($_.Exception.Message)" "ERROR"
        }

    } catch {
        Write-Status "Error during WMI data source modifications: $($_.Exception.Message)" "ERROR"
    }
}

# -------------------------------------------------------------------------------------------------------
# Hardware Device Management

if ($hardware) {
    Write-Status "Starting hardware device management..." "INFO"

    try {
        # List of virtual devices to hide/disable
        $virtualDevices = @(
            "*VBox*",
            "*VMware*",
            "*Virtual*",
            "*VMMEMCTL*",
            "*vm3dmp*",
            "*vmci*",
            "*vmhgfs*",
            "*vmmouse*",
            "*vmrawdsk*",
            "*vmusbmouse*"
        )

        # Try to disable virtual devices using PowerShell
        foreach ($devicePattern in $virtualDevices) {
            try {
                $devices = Get-PnpDevice | Where-Object { $_.FriendlyName -like $devicePattern -or $_.InstanceId -like $devicePattern }
                foreach ($device in $devices) {
                    if ($device.Status -eq "OK") {
                        try {
                            Disable-PnpDevice -InstanceId $device.InstanceId -Confirm:$false -ErrorAction Stop
                            Write-Status "Disabled device: $($device.FriendlyName)" "SUCCESS"
                        } catch {
                            Write-Status "Could not disable device $($device.FriendlyName): $($_.Exception.Message)" "WARNING"
                        }
                    }
                }
            } catch {
                Write-Status "Error processing device pattern $devicePattern" "WARNING"
            }
        }

    } catch {
        Write-Status "Error during hardware device management: $($_.Exception.Message)" "ERROR"
    }
}

# -------------------------------------------------------------------------------------------------------
# Network Adapter Modifications

if ($network) {
    Write-Status "Starting network adapter modifications..." "INFO"

    try {
        # Modify network adapter descriptions and names
        $adapters = Get-NetAdapter

        foreach ($adapter in $adapters) {
            if ($adapter.Name -like "*VMware*" -or $adapter.Name -like "*VirtualBox*" -or $adapter.InterfaceDescription -like "*VMware*" -or $adapter.InterfaceDescription -like "*VirtualBox*") {

                $newName = "Ethernet $(Get-Random -Minimum 1 -Maximum 10)"
                $newDescription = "Intel(R) Ethernet Connection"

                try {
                    # Rename adapter
                    Rename-NetAdapter -Name $adapter.Name -NewName $newName -ErrorAction Stop
                    Write-Status "Renamed network adapter '$($adapter.Name)' to '$newName'" "SUCCESS"
                } catch {
                    Write-Status "Could not rename adapter '$($adapter.Name)': $($_.Exception.Message)" "WARNING"
                }

                # Modify registry entries for adapter description
                try {
                    $adapterGuid = $adapter.InterfaceGuid
                    $regPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e972-e325-11ce-bfc1-08002be10318}"

                    $subKeys = Get-ChildItem -Path $regPath -ErrorAction SilentlyContinue
                    foreach ($subKey in $subKeys) {
                        $netCfgInstanceId = Get-ItemProperty -Path $subKey.PSPath -Name "NetCfgInstanceId" -ErrorAction SilentlyContinue
                        if ($netCfgInstanceId -and $netCfgInstanceId.NetCfgInstanceId -eq $adapterGuid) {
                            Set-ItemProperty -Path $subKey.PSPath -Name "DriverDesc" -Value $newDescription -ErrorAction SilentlyContinue
                            Write-Status "Modified adapter description for $newName" "SUCCESS"
                            break
                        }
                    }
                } catch {
                    Write-Status "Could not modify adapter description: $($_.Exception.Message)" "WARNING"
                }
            }
        }

    } catch {
        Write-Status "Error during network adapter modifications: $($_.Exception.Message)" "ERROR"
    }
}

# -------------------------------------------------------------------------------------------------------
# Rename VMware Files

if ($files) {
	
	# Rename VMware directories

	Write-Output "[*] Attempting to rename C:\Program Files\Common Files\VMware directory..."

    	$VMwareCommonFiles = "C:\Program Files\Common Files\VMware"

    	if (Test-Path -Path $VMwareCommonFiles) {
        	Rename-Item $VMwareCommonFiles "C:\Program Files\Common Files\$(Get-RandomString)"
   	 }

    	else {
			Write-Output "[!] C:\Program Files\Common Files\VMware directory does not exist!"
    	}

    	Write-Output "[*] Attempting to rename C:\Program Files\VMware directory..."

    	$VMwareProgramDir = "C:\Program Files\VMware"

    	if (Test-Path -Path $VMwareProgramDir) {
        	Rename-Item $VMwareProgramDir "C:\Program Files\$(Get-RandomString)"
    	}

     	else {
        	Write-Output "[!] C:\Program Files\VMware directory does not exist!"
   	}
	
	# Rename VMware driver files

    Write-Output "[*] Attempting to rename VMware driver files in C:\Windows\System32\drivers\..."
	
	$path = "C:\Windows\System32\drivers\"
	
	$file_list ="vmhgfs.sys",
		"vmmemctl.sys",
		"vmmouse.sys",
		"vmrawdsk.sys",
		"vmusbmouse.sys"
	
    	foreach ($file in $file_list) {

		Write-Output "[*] Attempting to rename $file..."
		
		try {
			# We are renaming these files, as opposed to removing them, because Windows doesn't care if we just rename them
			Rename-Item "$path$file" "$path$(Get-RandomString).sys" -ErrorAction Stop
		}
		
		catch {
			Write-Output "[!] File does not seem to exist! Skipping..."
		}
	}

	$wildcardPattern = "vm3*.sys"
	$filesToRename = Get-ChildItem -Path $path -Filter $wildcardPattern
	
	foreach ($file in $filesToRename) {
   
    		Write-Output "[*] Attempting to rename $file..."
			Rename-Item "$path$file" "$path$(Get-RandomString).dll"
	}
	
	# Rename VMware system files (System32)
	
    Write-Output "[*] Attempting to rename DLL files in C:\Windows\System32\..."
	
	$path = "C:\Windows\System32\"
	
	$file_list = "vmhgfs.dll", "VMWSU.DLL"

    	foreach ($file in $file_list) {

		Write-Output "[*] Attempting to rename $file..."
		
		try {
			Rename-Item "$path$file" "$path$(Get-RandomString).dll" -ErrorAction Stop
		}
		
		catch {
			Write-Output "[!] File does not seem to exist! Skipping..."
		}
	}
	
	$wildcardPattern1 = "vm3*.dll"
	$wildcardPattern2 = "vmGuestLib*.dll"
	
	$filesToRename1 = Get-ChildItem -Path $path -Filter $wildcardPattern1
	$filesToRename2 = Get-ChildItem -Path $path -Filter $wildcardPattern2
	
	foreach ($file in $filesToRename1) {
   
    		Write-Output "[*] Attempting to rename $file..."
    		Rename-Item "$path$file" "$path$(Get-RandomString).dll"
	}
	
	foreach ($file in $filesToRename2) {
   
    		Write-Output "[*] Attempting to rename $file..."
    		Rename-Item "$path$file" "$path$(Get-RandomString).dll"
	}
	
	# Rename VMware system files (SysWOW64)
	
    Write-Output "[*] Attempting to rename system files in C:\Windows\SysWOW64\..."
	
	$path = "C:\Windows\SysWOW64\"
	
	$file_list = "vmhgfs.dll", "VMWSU.DLL"

    	foreach ($file in $file_list) {

		Write-Output "[*] Attempting to rename $file..."
		
		try {
			Rename-Item "$path$file" "$path$(Get-RandomString).dll" -ErrorAction Stop
		}
		
		catch {
			Write-Output "[!] File does not seem to exist! Skipping..."
		}
	}
	
	$wildcardPattern1 = "vm3*.dll"
	$wildcardPattern2 = "vmGuestLib*.dll"
	
	$filesToRename1 = Get-ChildItem -Path $path -Filter $wildcardPattern1
	$filesToRename2 = Get-ChildItem -Path $path -Filter $wildcardPattern2
	
	foreach ($file in $filesToRename1) {
   
    		Write-Output "[*] Attempting to rename $file..."
    		Rename-Item "$path$file" "$path$(Get-RandomString).dll"
	}
	
	foreach ($file in $filesToRename2) {
   
    		Write-Output "[*] Attempting to rename $file..."
    		Rename-Item "$path$file" "$path$(Get-RandomString).dll"
	}
}

# -------------------------------------------------------------------------------------------------------
# Multi-Hypervisor Evasion

if ($multi) {
    Write-Status "Starting multi-hypervisor evasion..." "INFO"

    # VirtualBox evasion
    Write-Status "Applying VirtualBox evasion techniques..." "INFO"

    # VirtualBox registry keys
    $vboxRegKeys = @(
        "HKLM:\HARDWARE\ACPI\DSDT\VBOX__",
        "HKLM:\HARDWARE\ACPI\FADT\VBOX__",
        "HKLM:\HARDWARE\ACPI\RSDT\VBOX__",
        "HKLM:\SOFTWARE\Oracle\VirtualBox Guest Additions",
        "HKLM:\SYSTEM\ControlSet001\Services\VBoxGuest",
        "HKLM:\SYSTEM\ControlSet001\Services\VBoxMouse",
        "HKLM:\SYSTEM\ControlSet001\Services\VBoxService",
        "HKLM:\SYSTEM\ControlSet001\Services\VBoxSF",
        "HKLM:\SYSTEM\ControlSet001\Services\VBoxVideo"
    )

    foreach ($regKey in $vboxRegKeys) {
        if (Test-Path $regKey) {
            try {
                Remove-Item -Path $regKey -Recurse -Force -ErrorAction Stop
                Write-Status "Removed VirtualBox registry key: $regKey" "SUCCESS"
            } catch {
                Write-Status "Could not remove VirtualBox registry key $regKey" "WARNING"
            }
        }
    }

    # VirtualBox processes
    $vboxProcesses = @("VBoxService", "VBoxTray", "VBoxControl")
    foreach ($proc in $vboxProcesses) {
        $process = Get-Process $proc -ErrorAction SilentlyContinue
        if ($process) {
            try {
                $process | Stop-Process -Force
                Write-Status "Killed VirtualBox process: $proc" "SUCCESS"
            } catch {
                Write-Status "Could not kill VirtualBox process: $proc" "WARNING"
            }
        }
    }

    # VirtualBox files
    $vboxFiles = @(
        "C:\Windows\System32\drivers\VBoxMouse.sys",
        "C:\Windows\System32\drivers\VBoxGuest.sys",
        "C:\Windows\System32\drivers\VBoxSF.sys",
        "C:\Windows\System32\drivers\VBoxVideo.sys",
        "C:\Windows\System32\vboxdisp.dll",
        "C:\Windows\System32\vboxhook.dll",
        "C:\Windows\System32\vboxmrxnp.dll",
        "C:\Windows\System32\vboxogl.dll",
        "C:\Windows\System32\vboxservice.exe",
        "C:\Windows\System32\vboxtray.exe",
        "C:\Windows\System32\VBoxControl.exe"
    )

    foreach ($file in $vboxFiles) {
        if (Test-Path $file) {
            try {
                Rename-Item $file "$file.$(Get-RandomString -Length 5)" -ErrorAction Stop
                Write-Status "Renamed VirtualBox file: $file" "SUCCESS"
            } catch {
                Write-Status "Could not rename VirtualBox file: $file" "WARNING"
            }
        }
    }

    # Hyper-V evasion
    Write-Status "Applying Hyper-V evasion techniques..." "INFO"

    # Hyper-V registry modifications
    $hypervRegKeys = @(
        @{Path="HKLM:\SOFTWARE\Microsoft\Virtual Machine\Guest\Parameters"; Action="Remove"},
        @{Path="HKLM:\SYSTEM\ControlSet001\Services\vmbus"; Action="Remove"},
        @{Path="HKLM:\SYSTEM\ControlSet001\Services\VMBusHID"; Action="Remove"}
    )

    foreach ($regKey in $hypervRegKeys) {
        if (Test-Path $regKey.Path) {
            try {
                if ($regKey.Action -eq "Remove") {
                    Remove-Item -Path $regKey.Path -Recurse -Force -ErrorAction Stop
                    Write-Status "Removed Hyper-V registry key: $($regKey.Path)" "SUCCESS"
                }
            } catch {
                Write-Status "Could not process Hyper-V registry key: $($regKey.Path)" "WARNING"
            }
        }
    }

    # QEMU/KVM evasion
    Write-Status "Applying QEMU/KVM evasion techniques..." "INFO"

    # QEMU processes
    $qemuProcesses = @("qemu-ga")
    foreach ($proc in $qemuProcesses) {
        $process = Get-Process $proc -ErrorAction SilentlyContinue
        if ($process) {
            try {
                $process | Stop-Process -Force
                Write-Status "Killed QEMU process: $proc" "SUCCESS"
            } catch {
                Write-Status "Could not kill QEMU process: $proc" "WARNING"
            }
        }
    }

    # Modify QEMU-specific registry entries
    $qemuRegModifications = @(
        @{Path="HKLM:\HARDWARE\DEVICEMAP\Scsi\Scsi Port 0\Scsi Bus 0\Target Id 0\Logical Unit Id 0"; Name="Identifier"; Value="$(Get-RandomString) SCSI Disk Device"},
        @{Path="HKLM:\HARDWARE\DESCRIPTION\System"; Name="SystemBiosVersion"; Value="$(Get-RandomString -Length 8)"}
    )

    foreach ($mod in $qemuRegModifications) {
        if (Test-Path $mod.Path) {
            try {
                $currentValue = Get-ItemProperty -Path $mod.Path -Name $mod.Name -ErrorAction SilentlyContinue
                if ($currentValue -and ($currentValue.$($mod.Name) -like "*QEMU*" -or $currentValue.$($mod.Name) -like "*BOCHS*")) {
                    Set-ItemProperty -Path $mod.Path -Name $mod.Name -Value $mod.Value -ErrorAction Stop
                    Write-Status "Modified QEMU registry entry: $($mod.Path)\$($mod.Name)" "SUCCESS"
                }
            } catch {
                Write-Status "Could not modify QEMU registry entry: $($mod.Path)\$($mod.Name)" "WARNING"
            }
        }
    }
}

# -------------------------------------------------------------------------------------------------------
# Validation and Testing

if ($validate) {
    Write-Status "Running validation checks..." "INFO"

    $detectionCount = 0
    $totalChecks = 0

    # Check for remaining VM artifacts
    Write-Status "Checking for remaining VM artifacts..." "INFO"

    # Registry checks
    $vmRegKeys = @(
        "HKLM:\SOFTWARE\VMware, Inc.",
        "HKLM:\SOFTWARE\Oracle\VirtualBox Guest Additions",
        "HKLM:\SYSTEM\ControlSet001\Services\VBoxGuest",
        "HKLM:\SYSTEM\ControlSet001\Services\vmtools"
    )

    foreach ($regKey in $vmRegKeys) {
        $totalChecks++
        if (Test-Path $regKey) {
            Write-Status "DETECTION: Registry key still exists: $regKey" "ERROR"
            $detectionCount++
        }
    }

    # Process checks
    $vmProcesses = @("vmtoolsd", "VBoxService", "VBoxTray", "qemu-ga")
    foreach ($proc in $vmProcesses) {
        $totalChecks++
        if (Get-Process $proc -ErrorAction SilentlyContinue) {
            Write-Status "DETECTION: VM process still running: $proc" "ERROR"
            $detectionCount++
        }
    }

    # File checks
    $vmFiles = @(
        "C:\Windows\System32\drivers\vmhgfs.sys",
        "C:\Windows\System32\drivers\VBoxGuest.sys",
        "C:\Program Files\VMware",
        "C:\Program Files\Oracle\VirtualBox Guest Additions"
    )

    foreach ($file in $vmFiles) {
        $totalChecks++
        if (Test-Path $file) {
            Write-Status "DETECTION: VM file/directory still exists: $file" "ERROR"
            $detectionCount++
        }
    }

    # MAC address checks
    $adapters = Get-NetAdapter | Where-Object { $_.Status -eq "Up" }
    foreach ($adapter in $adapters) {
        $totalChecks++
        $mac = $adapter.MacAddress
        $vmMacPrefixes = @("00-05-69", "00-0C-29", "00-1C-14", "00-50-56", "08-00-27", "00-1C-42")
        foreach ($prefix in $vmMacPrefixes) {
            if ($mac.StartsWith($prefix)) {
                Write-Status "DETECTION: VM MAC address detected: $mac on $($adapter.Name)" "ERROR"
                $detectionCount++
                break
            }
        }
    }

    # Summary
    Write-Status "Validation complete: $detectionCount detections out of $totalChecks checks" "INFO"
    if ($detectionCount -eq 0) {
        Write-Status "EXCELLENT: No VM artifacts detected!" "SUCCESS"
    } elseif ($detectionCount -lt 5) {
        Write-Status "GOOD: Few VM artifacts remain" "WARNING"
    } else {
        Write-Status "POOR: Many VM artifacts still detectable" "ERROR"
    }
}

Write-Output ""
Write-Status "VMwareCloak Enhanced execution completed!" "SUCCESS"
Write-Output ""
Write-Output "** Enhanced VMwareCloak v0.5 - Comprehensive VM Detection Evasion **"
Write-Output "** Original by @d4rksystem (Kyle Cucci) **"
Write-Output "** Enhanced for modern malware analysis environments **"
Write-Output ""
Write-Output "Summary of applied techniques:"
if ($reg) { Write-Output "  [+] Registry modifications (original + enhanced)" }
if ($procs) { Write-Output "  [+] Process termination (VMware + multi-hypervisor)" }
if ($files) { Write-Output "  [+] File system modifications (enhanced coverage)" }
if ($mac) { Write-Output "  [+] MAC address spoofing to legitimate vendor patterns" }
if ($wmi) { Write-Output "  [+] WMI data source modifications (BIOS, hardware info)" }
if ($hardware) { Write-Output "  [+] Virtual hardware device hiding/disabling" }
if ($network) { Write-Output "  [+] Network adapter name and description changes" }
if ($multi) { Write-Output "  [+] Multi-hypervisor evasion (VBox, Hyper-V, QEMU)" }
if ($validate) { Write-Output "  [+] Validation checks performed" }
Write-Output ""
Write-Output "Recommendations for complete evasion:"
Write-Output "  * Configure hypervisor to disable CPUID hypervisor bit"
Write-Output "  * Use timing attack mitigation tools for RDTSC"
Write-Output "  * Consider ACPI/SMBIOS table modifications"
Write-Output "  * Test with pafish and al-khaser detection tools"
Write-Output "  * Revert VM to clean state when analysis is complete"
Write-Output ""
Write-Output "** Did you receive access errors? Run as SYSTEM (not just Administrator)! **"
Write-Output "** For issues or contributions: https://github.com/d4rksystem/VMwareCloak **"

