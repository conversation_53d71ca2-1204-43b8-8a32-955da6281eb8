# External Tools Configuration Guide

This guide provides detailed instructions for configuring external tools and hypervisor settings to achieve complete VM detection evasion beyond what PowerShell can accomplish.

## Hypervisor Configuration

### VMware Workstation/Player Configuration

#### .vmx File Modifications
Add these lines to your virtual machine's `.vmx` configuration file:

```ini
# === CPUID Evasion ===
# Hide hypervisor presence bit
hypervisor.cpuid.v0 = "FALSE"
cpuid.1.eax = "00000f4a"
cpuid.1.ebx = "02100800"
cpuid.1.ecx = "00000209"
cpuid.1.edx = "078bfbfd"

# === Timing Attack Mitigation ===
# RDTSC offset configuration
rdtsc.offset = "0"
monitor_control.restrict_backdoor = "TRUE"

# === VM Detection Evasion ===
# Disable VM-specific features
isolation.tools.unity.disable = "TRUE"
isolation.tools.unityInterlockOperation.disable = "TRUE"
isolation.tools.unity.push.enable = "FALSE"
isolation.tools.unity.taskbar.enable = "FALSE"
isolation.tools.hgfs.disable = "TRUE"
isolation.tools.dnd.disable = "TRUE"
isolation.tools.copy.enable = "FALSE"
isolation.tools.paste.enabled = "FALSE"

# === Hardware Spoofing ===
# SMBIOS modifications
smbios.reflecthost = "TRUE"
hw.model = "OptiPlex 7090"
hw.model.reflecthost = "FALSE"

# === Advanced Stealth ===
# Disable VMware-specific devices
ethernet0.virtualDev = "e1000e"
sound.virtualDev = "hdaudio"
usb.present = "FALSE"
ehci.present = "FALSE"
usb_xhci.present = "FALSE"

# === Memory Management ===
# Disable memory ballooning
sched.mem.pshare.enable = "FALSE"
MemTrimRate = "0"
mainMem.useNamedFile = "FALSE"
sched.mem.pshare.salt = "12345"

# === Logging and Monitoring ===
# Disable logging
logging = "FALSE"
log.fileName = ""
log.append = "FALSE"
log.keepOld = "0"
```

#### VMware Tools Removal
```bash
# Uninstall VMware Tools completely
# From Windows guest:
# 1. Control Panel > Programs > Uninstall VMware Tools
# 2. Manually delete remaining files:
rmdir /s "C:\Program Files\VMware"
rmdir /s "C:\Program Files\Common Files\VMware"
```

### VirtualBox Configuration

#### VBoxManage Commands
```bash
# Disable paravirtualization
VBoxManage modifyvm "YourVM" --paravirtprovider none

# CPUID modifications
VBoxManage modifyvm "YourVM" --cpuidset 00000001 000106e5 00100800 0098e3fd bfebfbff
VBoxManage modifyvm "YourVM" --cpuidset 80000001 00000000 00000000 00000001 28100000

# Hardware modifications
VBoxManage modifyvm "YourVM" --biosbootmenu disabled
VBoxManage modifyvm "YourVM" --bioslogofadein off
VBoxManage modifyvm "YourVM" --bioslogofadeout off
VBoxManage modifyvm "YourVM" --bioslogodisplaytime 0

# System information spoofing
VBoxManage setextradata "YourVM" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVendor" "American Megatrends Inc."
VBoxManage setextradata "YourVM" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVersion" "2.15.1236"
VBoxManage setextradata "YourVM" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVendor" "Dell Inc."
VBoxManage setextradata "YourVM" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemProduct" "OptiPlex 7090"
VBoxManage setextradata "YourVM" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVersion" "1.0"
VBoxManage setextradata "YourVM" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSerial" "BXTNX53"

# Disable guest additions
VBoxManage modifyvm "YourVM" --clipboard disabled
VBoxManage modifyvm "YourVM" --draganddrop disabled
```

#### VirtualBox Guest Additions Removal
```bash
# From Windows guest:
# 1. Control Panel > Programs > Uninstall Oracle VM VirtualBox Guest Additions
# 2. Boot from safe mode and delete:
rmdir /s "C:\Program Files\Oracle"
del "C:\Windows\System32\VBox*"
del "C:\Windows\System32\drivers\VBox*"
```

### Hyper-V Configuration

#### PowerShell Commands
```powershell
# Disable Hyper-V enlightenments
Set-VMProcessor -VMName "YourVM" -ExposeVirtualizationExtensions $false
Set-VMProcessor -VMName "YourVM" -HwThreadCountPerCore 1

# Disable integration services
Disable-VMIntegrationService -VMName "YourVM" -Name "Guest Service Interface"
Disable-VMIntegrationService -VMName "YourVM" -Name "Heartbeat"
Disable-VMIntegrationService -VMName "YourVM" -Name "Key-Value Pair Exchange"
Disable-VMIntegrationService -VMName "YourVM" -Name "Shutdown"
Disable-VMIntegrationService -VMName "YourVM" -Name "Time Synchronization"
Disable-VMIntegrationService -VMName "YourVM" -Name "VSS"

# Hardware modifications
Set-VMMemory -VMName "YourVM" -DynamicMemoryEnabled $false
Set-VMProcessor -VMName "YourVM" -Count 4
```

## Third-Party Tools

### CPUID Manipulation Tools

#### InvisiMem (Research Tool)
**Purpose**: Kernel-level CPUID instruction hooking
**Status**: Research/academic use only
**Installation**: Requires signed kernel driver
**Configuration**:
```ini
[CPUID_Hooks]
EAX_1_ECX_31 = 0  # Hide hypervisor bit
EAX_40000000 = "GenuineIntel"  # Spoof hypervisor vendor
```

#### VT-x/AMD-V Disabling
**Method 1 - BIOS**: Disable hardware virtualization in host BIOS
**Method 2 - bcdedit**: 
```cmd
bcdedit /set hypervisorlaunchtype off
```

### Timing Attack Mitigation

#### TSC Synchronization Tools
**Purpose**: Normalize RDTSC instruction timing
**Implementation**: Kernel driver required
**Configuration**: Set consistent TSC frequency across cores

#### Sleep Acceleration Detection
**Tool**: Custom timing normalization driver
**Purpose**: Prevent sleep/delay acceleration detection
**Method**: Hook sleep functions to maintain realistic timing

### Memory Artifact Hiding

#### IDT/GDT Location Spoofing
**Requirement**: Ring-0 kernel driver
**Purpose**: Hide virtual machine memory layout signatures
**Implementation**: Hook SIDT/SGDT instructions

#### Page Table Modifications
**Tool**: Custom hypervisor or kernel driver
**Purpose**: Hide EPT (Extended Page Tables) signatures
**Complexity**: Requires advanced kernel programming

### Hardware Simulation Tools

#### ACPI Table Editor
**Tool**: ACPI Source Language (ASL) Compiler
**Purpose**: Modify system firmware tables
**Usage**:
```bash
# Extract ACPI tables
acpidump -o acpi_tables.dat

# Decompile and edit
iasl -d DSDT.aml
# Edit DSDT.dsl to remove VM signatures
iasl DSDT.dsl

# Replace in system (requires BIOS modification)
```

#### SMBIOS Modification
**Tool**: DMI/SMBIOS editors
**Purpose**: Change system management BIOS information
**Method**: Modify system firmware or use runtime patching

### Network Stack Modifications

#### MAC Address Persistence
**Issue**: MAC changes may not persist across reboots
**Solution**: Registry modification + driver configuration
```powershell
# Persistent MAC address change
$regPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e972-e325-11ce-bfc1-08002be10318}\0001"
Set-ItemProperty -Path $regPath -Name "NetworkAddress" -Value "001B21ABCDEF"
```

#### Network Driver Replacement
**Purpose**: Replace virtual network drivers with physical equivalents
**Method**: Driver signature bypass + custom driver installation

## Validation Tools

### Detection Testing Frameworks

#### Pafish (Paranoid Fish)
```bash
# Download from: https://github.com/a0rtega/pafish
# Compile and run
gcc -o pafish pafish.c
./pafish.exe
```

#### Al-Khaser
```bash
# Download from: https://github.com/ayoubfaouzi/al-khaser
# Run specific tests
al-khaser.exe --check VMWARE --check VBOX --check TIMING_ATTACKS --check CPUID
```

#### Custom Detection Scripts
```powershell
# CPUID hypervisor bit check
$cpuidResult = Get-WmiObject -Query "SELECT * FROM Win32_Processor"
if ($cpuidResult.Description -like "*Virtual*") {
    Write-Host "Virtual processor detected"
}

# Timing attack simulation
$start = Get-Date
Start-Sleep -Milliseconds 1000
$end = Get-Date
$elapsed = ($end - $start).TotalMilliseconds
if ($elapsed -lt 100) {
    Write-Host "Sleep acceleration detected"
}
```

## Automation Scripts

### Complete Setup Script
```powershell
# VMware complete evasion setup
param([string]$vmxPath)

# Backup original .vmx file
Copy-Item $vmxPath "$vmxPath.backup"

# Apply all VMware evasion settings
$evasionSettings = @"
hypervisor.cpuid.v0 = "FALSE"
rdtsc.offset = "0"
isolation.tools.unity.disable = "TRUE"
isolation.tools.hgfs.disable = "TRUE"
smbios.reflecthost = "TRUE"
"@

Add-Content -Path $vmxPath -Value $evasionSettings
Write-Host "VMware evasion settings applied to $vmxPath"
```

### Validation Automation
```powershell
# Automated validation pipeline
.\VMwareCloak.ps1 -all
Start-Sleep -Seconds 10
.\VMware-Detection-Test.ps1 -detailed -export
.\pafish.exe > pafish_results.txt
.\al-khaser.exe --check ALL > alkhaser_results.txt
```

## Troubleshooting

### Common Issues

#### VM Won't Boot After Modifications
**Solution**: Revert to backup .vmx file or VM snapshot
**Prevention**: Always create snapshots before major changes

#### Network Connectivity Lost
**Cause**: MAC address changes or driver modifications
**Solution**: Reset network adapters or revert network settings

#### Performance Degradation
**Cause**: Disabled VM optimization features
**Solution**: Balance evasion vs. performance based on analysis needs

#### Detection Still Occurring
**Cause**: Incomplete evasion or new detection methods
**Solution**: Use validation tools to identify remaining artifacts

### Recovery Procedures

#### Emergency VM Recovery
1. Boot from safe mode
2. Restore registry from backup
3. Reinstall network drivers
4. Revert to clean snapshot if necessary

#### Hypervisor Reset
1. Power off VM completely
2. Restore original configuration files
3. Clear VM cache and logs
4. Restart hypervisor service

---

**Document Version**: 1.0  
**Last Updated**: 2024  
**Compatibility**: VMware, VirtualBox, Hyper-V  
**Warning**: Use only in isolated research environments
