#################################################
## VMware-Detection-Test.ps1: A comprehensive validation script to test VM detection evasion effectiveness
## Companion script for VMwareCloak Enhanced
## Tests against common malware VM detection techniques
##################################################
## Author: Enhanced VMwareCloak Project
## Version: 1.0
##################################################

param (
    [switch]$detailed = $false,
    [switch]$export = $false,
    [string]$outputFile = "vm-detection-results.txt"
)

function Write-TestResult {
    param([string]$Test, [string]$Result, [string]$Details = "")
    $timestamp = Get-Date -Format "HH:mm:ss"
    $status = if ($Result -eq "PASS") { "✓" } else { "✗" }
    $color = if ($Result -eq "PASS") { "Green" } else { "Red" }
    
    $output = "[$timestamp] $status $Test : $Result"
    if ($Details -and $detailed) {
        $output += " - $Details"
    }
    
    Write-Host $output -ForegroundColor $color
    
    if ($export) {
        Add-Content -Path $outputFile -Value $output
    }
}

function Test-RegistryArtifacts {
    Write-Host "`n=== Registry Artifact Tests ===" -ForegroundColor Yellow
    
    $registryTests = @(
        @{Path="HKLM:\SOFTWARE\VMware, Inc."; Name="VMware Registry Key"},
        @{Path="HKLM:\SOFTWARE\Oracle\VirtualBox Guest Additions"; Name="VirtualBox Registry Key"},
        @{Path="HKLM:\SYSTEM\ControlSet001\Services\VBoxGuest"; Name="VBoxGuest Service Key"},
        @{Path="HKLM:\SYSTEM\ControlSet001\Services\vmtools"; Name="VMware Tools Service Key"},
        @{Path="HKLM:\SYSTEM\ControlSet001\Services\VGAuthService"; Name="VGAuth Service Key"},
        @{Path="HKLM:\SOFTWARE\Microsoft\Virtual Machine\Guest\Parameters"; Name="Hyper-V Guest Parameters"}
    )
    
    $detectionCount = 0
    foreach ($test in $registryTests) {
        if (Test-Path $test.Path) {
            Write-TestResult $test.Name "FAIL" "Registry key exists: $($test.Path)"
            $detectionCount++
        } else {
            Write-TestResult $test.Name "PASS" "Registry key not found"
        }
    }
    
    # Test specific registry values
    $valueTests = @(
        @{Path="HKLM:\HARDWARE\DESCRIPTION\System\BIOS"; Name="BIOSVendor"; Pattern="*VMware*|*VBOX*|*innotek*"},
        @{Path="HKLM:\HARDWARE\DESCRIPTION\System\BIOS"; Name="SystemManufacturer"; Pattern="*VMware*|*innotek*|*Microsoft Corporation*"},
        @{Path="HKLM:\HARDWARE\DEVICEMAP\Scsi\Scsi Port 0\Scsi Bus 0\Target Id 0\Logical Unit Id 0"; Name="Identifier"; Pattern="*VMware*|*VBOX*"}
    )
    
    foreach ($test in $valueTests) {
        try {
            $value = Get-ItemProperty -Path $test.Path -Name $test.Name -ErrorAction SilentlyContinue
            if ($value -and $value.$($test.Name) -like $test.Pattern) {
                Write-TestResult "Registry Value: $($test.Name)" "FAIL" "Suspicious value: $($value.$($test.Name))"
                $detectionCount++
            } else {
                Write-TestResult "Registry Value: $($test.Name)" "PASS" "Value appears legitimate"
            }
        } catch {
            Write-TestResult "Registry Value: $($test.Name)" "PASS" "Registry path not accessible"
        }
    }
    
    return $detectionCount
}

function Test-ProcessArtifacts {
    Write-Host "`n=== Process Artifact Tests ===" -ForegroundColor Yellow
    
    $vmProcesses = @(
        "vmtoolsd", "vm3dservice", "VGAuthService", "VMwareService", "Vmwaretray", "Vmwareuser",
        "VBoxService", "VBoxTray", "VBoxControl", "qemu-ga", "prl_cc", "prl_tools", "xenservice"
    )
    
    $detectionCount = 0
    foreach ($proc in $vmProcesses) {
        $process = Get-Process $proc -ErrorAction SilentlyContinue
        if ($process) {
            Write-TestResult "Process: $proc" "FAIL" "VM process is running"
            $detectionCount++
        } else {
            Write-TestResult "Process: $proc" "PASS" "Process not found"
        }
    }
    
    return $detectionCount
}

function Test-FileSystemArtifacts {
    Write-Host "`n=== File System Artifact Tests ===" -ForegroundColor Yellow
    
    $vmFiles = @(
        "C:\Windows\System32\drivers\vmhgfs.sys",
        "C:\Windows\System32\drivers\vmmemctl.sys",
        "C:\Windows\System32\drivers\vmmouse.sys",
        "C:\Windows\System32\drivers\VBoxGuest.sys",
        "C:\Windows\System32\drivers\VBoxMouse.sys",
        "C:\Windows\System32\drivers\VBoxSF.sys",
        "C:\Program Files\VMware",
        "C:\Program Files\Oracle\VirtualBox Guest Additions",
        "C:\Windows\System32\vboxdisp.dll",
        "C:\Windows\System32\vmhgfs.dll"
    )
    
    $detectionCount = 0
    foreach ($file in $vmFiles) {
        if (Test-Path $file) {
            Write-TestResult "File: $(Split-Path $file -Leaf)" "FAIL" "VM file exists: $file"
            $detectionCount++
        } else {
            Write-TestResult "File: $(Split-Path $file -Leaf)" "PASS" "File not found"
        }
    }
    
    return $detectionCount
}

function Test-NetworkArtifacts {
    Write-Host "`n=== Network Artifact Tests ===" -ForegroundColor Yellow
    
    $detectionCount = 0
    $adapters = Get-NetAdapter | Where-Object { $_.Status -eq "Up" -or $_.Status -eq "Disconnected" }
    
    foreach ($adapter in $adapters) {
        $mac = $adapter.MacAddress
        $vmMacPrefixes = @("00-05-69", "00-0C-29", "00-1C-14", "00-50-56", "08-00-27", "00-1C-42", "00-16-3E", "0A-00-27")
        
        $isVmMac = $false
        foreach ($prefix in $vmMacPrefixes) {
            if ($mac.StartsWith($prefix)) {
                Write-TestResult "MAC Address: $($adapter.Name)" "FAIL" "VM MAC detected: $mac"
                $detectionCount++
                $isVmMac = $true
                break
            }
        }
        
        if (-not $isVmMac) {
            Write-TestResult "MAC Address: $($adapter.Name)" "PASS" "Legitimate MAC: $mac"
        }
        
        # Check adapter names
        if ($adapter.Name -like "*VMware*" -or $adapter.Name -like "*VirtualBox*" -or $adapter.InterfaceDescription -like "*VMware*" -or $adapter.InterfaceDescription -like "*VirtualBox*") {
            Write-TestResult "Adapter Name: $($adapter.Name)" "FAIL" "VM adapter name detected"
            $detectionCount++
        } else {
            Write-TestResult "Adapter Name: $($adapter.Name)" "PASS" "Generic adapter name"
        }
    }
    
    return $detectionCount
}

function Test-WMIArtifacts {
    Write-Host "`n=== WMI Artifact Tests ===" -ForegroundColor Yellow
    
    $detectionCount = 0
    
    # BIOS Information
    try {
        $bios = Get-WmiObject -Class Win32_Bios
        if ($bios.SerialNumber -like "*VMware*" -or $bios.SerialNumber -like "*0*" -or $bios.Version -like "*VBOX*") {
            Write-TestResult "WMI BIOS Serial" "FAIL" "Suspicious BIOS serial: $($bios.SerialNumber)"
            $detectionCount++
        } else {
            Write-TestResult "WMI BIOS Serial" "PASS" "BIOS serial appears legitimate"
        }
    } catch {
        Write-TestResult "WMI BIOS Serial" "PASS" "WMI query failed (good for evasion)"
    }
    
    # Computer System
    try {
        $system = Get-WmiObject -Class Win32_ComputerSystem
        if ($system.Manufacturer -like "*VMware*" -or $system.Manufacturer -like "*innotek*" -or $system.Model -like "*Virtual*") {
            Write-TestResult "WMI System Info" "FAIL" "VM system info: $($system.Manufacturer) $($system.Model)"
            $detectionCount++
        } else {
            Write-TestResult "WMI System Info" "PASS" "System info appears legitimate: $($system.Manufacturer) $($system.Model)"
        }
    } catch {
        Write-TestResult "WMI System Info" "PASS" "WMI query failed (good for evasion)"
    }
    
    # Processor Information
    try {
        $processor = Get-WmiObject -Class Win32_Processor
        if ($processor.Name -like "*Virtual*" -or $processor.Description -like "*Virtual*") {
            Write-TestResult "WMI Processor" "FAIL" "Virtual processor detected: $($processor.Name)"
            $detectionCount++
        } else {
            Write-TestResult "WMI Processor" "PASS" "Processor appears legitimate: $($processor.Name)"
        }
    } catch {
        Write-TestResult "WMI Processor" "PASS" "WMI query failed (good for evasion)"
    }
    
    return $detectionCount
}

function Test-HardwareArtifacts {
    Write-Host "`n=== Hardware Artifact Tests ===" -ForegroundColor Yellow
    
    $detectionCount = 0
    
    # PnP Devices
    try {
        $devices = Get-PnpDevice | Where-Object { $_.FriendlyName -like "*VMware*" -or $_.FriendlyName -like "*VirtualBox*" -or $_.FriendlyName -like "*Virtual*" }
        foreach ($device in $devices) {
            if ($device.Status -eq "OK") {
                Write-TestResult "Hardware Device: $($device.FriendlyName)" "FAIL" "Virtual device detected and active"
                $detectionCount++
            } else {
                Write-TestResult "Hardware Device: $($device.FriendlyName)" "PASS" "Virtual device disabled/hidden"
            }
        }
        
        if ($devices.Count -eq 0) {
            Write-TestResult "Hardware Devices" "PASS" "No virtual devices detected"
        }
    } catch {
        Write-TestResult "Hardware Devices" "PASS" "Device enumeration failed (good for evasion)"
    }
    
    return $detectionCount
}

function Test-ServiceArtifacts {
    Write-Host "`n=== Service Artifact Tests ===" -ForegroundColor Yellow
    
    $vmServices = @(
        "VMTools", "VBoxService", "VGAuthService", "vm3dservice", "VMwareCAFCommAmqpListener",
        "VMwareCAFManagementAgentHost", "vmvss", "VMwareHostd"
    )
    
    $detectionCount = 0
    foreach ($serviceName in $vmServices) {
        $service = Get-Service $serviceName -ErrorAction SilentlyContinue
        if ($service) {
            if ($service.Status -eq "Running") {
                Write-TestResult "Service: $serviceName" "FAIL" "VM service is running"
                $detectionCount++
            } else {
                Write-TestResult "Service: $serviceName" "PASS" "VM service stopped/disabled"
            }
        } else {
            Write-TestResult "Service: $serviceName" "PASS" "Service not found"
        }
    }
    
    return $detectionCount
}

# Main execution
Write-Host "╔══════════════════════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
Write-Host "║                    VMware Detection Test Suite v1.0                         ║" -ForegroundColor Cyan
Write-Host "║              Comprehensive VM Evasion Validation Tool                       ║" -ForegroundColor Cyan
Write-Host "╚══════════════════════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
Write-Host ""

if ($export) {
    "VMware Detection Test Results - $(Get-Date)" | Out-File -FilePath $outputFile
    "=" * 50 | Add-Content -Path $outputFile
}

$totalDetections = 0
$totalDetections += Test-RegistryArtifacts
$totalDetections += Test-ProcessArtifacts
$totalDetections += Test-FileSystemArtifacts
$totalDetections += Test-NetworkArtifacts
$totalDetections += Test-WMIArtifacts
$totalDetections += Test-HardwareArtifacts
$totalDetections += Test-ServiceArtifacts

Write-Host "`n" + "=" * 80 -ForegroundColor Cyan
Write-Host "FINAL RESULTS" -ForegroundColor Cyan
Write-Host "=" * 80 -ForegroundColor Cyan

if ($totalDetections -eq 0) {
    Write-Host "🎉 EXCELLENT: No VM artifacts detected! Perfect evasion achieved." -ForegroundColor Green
    $grade = "A+"
} elseif ($totalDetections -le 3) {
    Write-Host "✅ GOOD: $totalDetections minor artifacts detected. Very effective evasion." -ForegroundColor Yellow
    $grade = "B+"
} elseif ($totalDetections -le 7) {
    Write-Host "⚠️  FAIR: $totalDetections artifacts detected. Some evasion techniques working." -ForegroundColor Yellow
    $grade = "C"
} else {
    Write-Host "❌ POOR: $totalDetections artifacts detected. Significant VM fingerprints remain." -ForegroundColor Red
    $grade = "D"
}

Write-Host "`nEvasion Grade: $grade" -ForegroundColor Cyan
Write-Host "Total Detections: $totalDetections" -ForegroundColor Cyan

if ($export) {
    "`nFinal Results:" | Add-Content -Path $outputFile
    "Evasion Grade: $grade" | Add-Content -Path $outputFile
    "Total Detections: $totalDetections" | Add-Content -Path $outputFile
    Write-Host "`nResults exported to: $outputFile" -ForegroundColor Green
}

Write-Host "`nRecommendations:" -ForegroundColor Cyan
if ($totalDetections -gt 0) {
    Write-Host "• Run VMwareCloak.ps1 with -all parameter for comprehensive evasion" -ForegroundColor White
    Write-Host "• Ensure script was run with SYSTEM privileges" -ForegroundColor White
    Write-Host "• Consider hypervisor-level configuration changes for remaining detections" -ForegroundColor White
}
Write-Host "• Test with pafish.exe and al-khaser.exe for additional validation" -ForegroundColor White
Write-Host "• Create VM snapshot before malware analysis" -ForegroundColor White
